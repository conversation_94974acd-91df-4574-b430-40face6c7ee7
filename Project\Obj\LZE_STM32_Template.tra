*** Creating Trace Output File '.\Obj\LZE_STM32_Template.tra' Ok.
### Preparing for ADS-LD.
### Creating ADS-LD Command Line
### List of Objects: adding '".\obj\main.o"'
### List of Objects: adding '".\obj\delay.o"'
### List of Objects: adding '".\obj\mb1504.o"'
### List of Objects: adding '".\obj\peripheralinit.o"'
### List of Objects: adding '".\obj\misc.o"'
### List of Objects: adding '".\obj\stm32f10x_adc.o"'
### List of Objects: adding '".\obj\stm32f10x_bkp.o"'
### List of Objects: adding '".\obj\stm32f10x_can.o"'
### List of Objects: adding '".\obj\stm32f10x_cec.o"'
### List of Objects: adding '".\obj\stm32f10x_crc.o"'
### List of Objects: adding '".\obj\stm32f10x_dac.o"'
### List of Objects: adding '".\obj\stm32f10x_dbgmcu.o"'
### List of Objects: adding '".\obj\stm32f10x_dma.o"'
### List of Objects: adding '".\obj\stm32f10x_exti.o"'
### List of Objects: adding '".\obj\stm32f10x_flash.o"'
### List of Objects: adding '".\obj\stm32f10x_fsmc.o"'
### List of Objects: adding '".\obj\stm32f10x_gpio.o"'
### List of Objects: adding '".\obj\stm32f10x_i2c.o"'
### List of Objects: adding '".\obj\stm32f10x_iwdg.o"'
### List of Objects: adding '".\obj\stm32f10x_pwr.o"'
### List of Objects: adding '".\obj\stm32f10x_rcc.o"'
### List of Objects: adding '".\obj\stm32f10x_rtc.o"'
### List of Objects: adding '".\obj\stm32f10x_sdio.o"'
### List of Objects: adding '".\obj\stm32f10x_spi.o"'
### List of Objects: adding '".\obj\stm32f10x_tim.o"'
### List of Objects: adding '".\obj\stm32f10x_usart.o"'
### List of Objects: adding '".\obj\stm32f10x_wwdg.o"'
### List of Objects: adding '".\obj\core_cm3.o"'
### List of Objects: adding '".\obj\system_stm32f10x.o"'
### List of Objects: adding '".\obj\startup_stm32f10x_hd.o"'
### ADS-LD Command completed:
--cpu Cortex-M3 ".\obj\main.o" ".\obj\delay.o" ".\obj\mb1504.o" ".\obj\peripheralinit.o" ".\obj\misc.o" ".\obj\stm32f10x_adc.o" ".\obj\stm32f10x_bkp.o" ".\obj\stm32f10x_can.o" ".\obj\stm32f10x_cec.o" ".\obj\stm32f10x_crc.o" ".\obj\stm32f10x_dac.o" ".\obj\stm32f10x_dbgmcu.o" ".\obj\stm32f10x_dma.o" ".\obj\stm32f10x_exti.o" ".\obj\stm32f10x_flash.o" ".\obj\stm32f10x_fsmc.o" ".\obj\stm32f10x_gpio.o" ".\obj\stm32f10x_i2c.o" ".\obj\stm32f10x_iwdg.o" ".\obj\stm32f10x_pwr.o" ".\obj\stm32f10x_rcc.o" ".\obj\stm32f10x_rtc.o" ".\obj\stm32f10x_sdio.o" ".\obj\stm32f10x_spi.o" ".\obj\stm32f10x_tim.o" ".\obj\stm32f10x_usart.o" ".\obj\stm32f10x_wwdg.o" ".\obj\core_cm3.o" ".\obj\system_stm32f10x.o" ".\obj\startup_stm32f10x_hd.o" 

--strict --scatter ".\Obj\LZE_STM32_Template.sct" 

--summary_stderr --info summarysizes --map --xref --callgraph --symbols 

--info sizes --info totals --info unused --info veneers 

 --list ".\List\LZE_STM32_Template.map" -o .\Obj\LZE_STM32_Template.axf### Preparing Environment (PrepEnvAds)
### ADS-LD Output File: '.\Obj\LZE_STM32_Template.axf'
### ADS-LD Command File: '.\Obj\LZE_STM32_Template.lnp'
### Checking for dirty Components...
### Creating CmdFile '.\Obj\LZE_STM32_Template.lnp', Handle=0x00000438
### Writing '.lnp' file
### ADS-LD Command file '.\Obj\LZE_STM32_Template.lnp' is ready.
### ADS-LD: About to start ADS-LD Thread.
### ADS-LD: executed with 0 errors
### Updating obj list
### LDADS_file() completed.
