Dependencies for Project 'LZE_STM32_Template', Target 'LZE_STM32_Template': (DO NOT MODIFY !)
F (..\User\main.c)(0x5358049A)(-c --cpu Cortex-M3 -g -O0 --apcs=interwork -I..\CMSIS\CoreSupport -I..\CMSIS\DeviceSupport\STM32F10x -I..\STM32F10x_StdPeriph_Driver\inc -I..\User 

-I "F:\keil  MDK\ARM\RV31\INC" 

-I "F:\keil  MDK\ARM\CMSIS\Include" 

-I "F:\keil  MDK\ARM\Inc\ST\STM32F10x" 

-DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD -o ".\Obj\main.o" --omf_browse ".\Obj\main.crf" --depend ".\Obj\main.d")
F (..\STM32F10x_StdPeriph_Driver\src\misc.c)(0x4D783BB4)(-c --cpu Cortex-M3 -g -O0 --apcs=interwork -I..\CMSIS\CoreSupport -I..\CMSIS\DeviceSupport\STM32F10x -I..\STM32F10x_StdPeriph_Driver\inc -I..\User 

-I "F:\keil  MDK\ARM\RV31\INC" 

-I "F:\keil  MDK\ARM\CMSIS\Include" 

-I "F:\keil  MDK\ARM\Inc\ST\STM32F10x" 

-DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD -o ".\Obj\misc.o" --omf_browse ".\Obj\misc.crf" --depend ".\Obj\misc.d")
F (..\STM32F10x_StdPeriph_Driver\src\stm32f10x_adc.c)(0x4D783BB4)(-c --cpu Cortex-M3 -g -O0 --apcs=interwork -I..\CMSIS\CoreSupport -I..\CMSIS\DeviceSupport\STM32F10x -I..\STM32F10x_StdPeriph_Driver\inc -I..\User 

-I "F:\keil  MDK\ARM\RV31\INC" 

-I "F:\keil  MDK\ARM\CMSIS\Include" 

-I "F:\keil  MDK\ARM\Inc\ST\STM32F10x" 

-DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD -o ".\Obj\stm32f10x_adc.o" --omf_browse ".\Obj\stm32f10x_adc.crf" --depend ".\Obj\stm32f10x_adc.d")
F (..\STM32F10x_StdPeriph_Driver\src\stm32f10x_bkp.c)(0x4D783BB4)(-c --cpu Cortex-M3 -g -O0 --apcs=interwork -I..\CMSIS\CoreSupport -I..\CMSIS\DeviceSupport\STM32F10x -I..\STM32F10x_StdPeriph_Driver\inc -I..\User 

-I "F:\keil  MDK\ARM\RV31\INC" 

-I "F:\keil  MDK\ARM\CMSIS\Include" 

-I "F:\keil  MDK\ARM\Inc\ST\STM32F10x" 

-DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD -o ".\Obj\stm32f10x_bkp.o" --omf_browse ".\Obj\stm32f10x_bkp.crf" --depend ".\Obj\stm32f10x_bkp.d")
F (..\STM32F10x_StdPeriph_Driver\src\stm32f10x_can.c)(0x4D783BB4)(-c --cpu Cortex-M3 -g -O0 --apcs=interwork -I..\CMSIS\CoreSupport -I..\CMSIS\DeviceSupport\STM32F10x -I..\STM32F10x_StdPeriph_Driver\inc -I..\User 

-I "F:\keil  MDK\ARM\RV31\INC" 

-I "F:\keil  MDK\ARM\CMSIS\Include" 

-I "F:\keil  MDK\ARM\Inc\ST\STM32F10x" 

-DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD -o ".\Obj\stm32f10x_can.o" --omf_browse ".\Obj\stm32f10x_can.crf" --depend ".\Obj\stm32f10x_can.d")
F (..\STM32F10x_StdPeriph_Driver\src\stm32f10x_cec.c)(0x4D783BB4)(-c --cpu Cortex-M3 -g -O0 --apcs=interwork -I..\CMSIS\CoreSupport -I..\CMSIS\DeviceSupport\STM32F10x -I..\STM32F10x_StdPeriph_Driver\inc -I..\User 

-I "F:\keil  MDK\ARM\RV31\INC" 

-I "F:\keil  MDK\ARM\CMSIS\Include" 

-I "F:\keil  MDK\ARM\Inc\ST\STM32F10x" 

-DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD -o ".\Obj\stm32f10x_cec.o" --omf_browse ".\Obj\stm32f10x_cec.crf" --depend ".\Obj\stm32f10x_cec.d")
F (..\STM32F10x_StdPeriph_Driver\src\stm32f10x_crc.c)(0x4D783BB4)(-c --cpu Cortex-M3 -g -O0 --apcs=interwork -I..\CMSIS\CoreSupport -I..\CMSIS\DeviceSupport\STM32F10x -I..\STM32F10x_StdPeriph_Driver\inc -I..\User 

-I "F:\keil  MDK\ARM\RV31\INC" 

-I "F:\keil  MDK\ARM\CMSIS\Include" 

-I "F:\keil  MDK\ARM\Inc\ST\STM32F10x" 

-DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD -o ".\Obj\stm32f10x_crc.o" --omf_browse ".\Obj\stm32f10x_crc.crf" --depend ".\Obj\stm32f10x_crc.d")
F (..\STM32F10x_StdPeriph_Driver\src\stm32f10x_dac.c)(0x4D783BB4)(-c --cpu Cortex-M3 -g -O0 --apcs=interwork -I..\CMSIS\CoreSupport -I..\CMSIS\DeviceSupport\STM32F10x -I..\STM32F10x_StdPeriph_Driver\inc -I..\User 

-I "F:\keil  MDK\ARM\RV31\INC" 

-I "F:\keil  MDK\ARM\CMSIS\Include" 

-I "F:\keil  MDK\ARM\Inc\ST\STM32F10x" 

-DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD -o ".\Obj\stm32f10x_dac.o" --omf_browse ".\Obj\stm32f10x_dac.crf" --depend ".\Obj\stm32f10x_dac.d")
F (..\STM32F10x_StdPeriph_Driver\src\stm32f10x_dbgmcu.c)(0x4D783BB4)(-c --cpu Cortex-M3 -g -O0 --apcs=interwork -I..\CMSIS\CoreSupport -I..\CMSIS\DeviceSupport\STM32F10x -I..\STM32F10x_StdPeriph_Driver\inc -I..\User 

-I "F:\keil  MDK\ARM\RV31\INC" 

-I "F:\keil  MDK\ARM\CMSIS\Include" 

-I "F:\keil  MDK\ARM\Inc\ST\STM32F10x" 

-DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD -o ".\Obj\stm32f10x_dbgmcu.o" --omf_browse ".\Obj\stm32f10x_dbgmcu.crf" --depend ".\Obj\stm32f10x_dbgmcu.d")
F (..\STM32F10x_StdPeriph_Driver\src\stm32f10x_dma.c)(0x4D783BB4)(-c --cpu Cortex-M3 -g -O0 --apcs=interwork -I..\CMSIS\CoreSupport -I..\CMSIS\DeviceSupport\STM32F10x -I..\STM32F10x_StdPeriph_Driver\inc -I..\User 

-I "F:\keil  MDK\ARM\RV31\INC" 

-I "F:\keil  MDK\ARM\CMSIS\Include" 

-I "F:\keil  MDK\ARM\Inc\ST\STM32F10x" 

-DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD -o ".\Obj\stm32f10x_dma.o" --omf_browse ".\Obj\stm32f10x_dma.crf" --depend ".\Obj\stm32f10x_dma.d")
F (..\STM32F10x_StdPeriph_Driver\src\stm32f10x_exti.c)(0x4D783BB4)(-c --cpu Cortex-M3 -g -O0 --apcs=interwork -I..\CMSIS\CoreSupport -I..\CMSIS\DeviceSupport\STM32F10x -I..\STM32F10x_StdPeriph_Driver\inc -I..\User 

-I "F:\keil  MDK\ARM\RV31\INC" 

-I "F:\keil  MDK\ARM\CMSIS\Include" 

-I "F:\keil  MDK\ARM\Inc\ST\STM32F10x" 

-DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD -o ".\Obj\stm32f10x_exti.o" --omf_browse ".\Obj\stm32f10x_exti.crf" --depend ".\Obj\stm32f10x_exti.d")
F (..\STM32F10x_StdPeriph_Driver\src\stm32f10x_flash.c)(0x4D783BB4)(-c --cpu Cortex-M3 -g -O0 --apcs=interwork -I..\CMSIS\CoreSupport -I..\CMSIS\DeviceSupport\STM32F10x -I..\STM32F10x_StdPeriph_Driver\inc -I..\User 

-I "F:\keil  MDK\ARM\RV31\INC" 

-I "F:\keil  MDK\ARM\CMSIS\Include" 

-I "F:\keil  MDK\ARM\Inc\ST\STM32F10x" 

-DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD -o ".\Obj\stm32f10x_flash.o" --omf_browse ".\Obj\stm32f10x_flash.crf" --depend ".\Obj\stm32f10x_flash.d")
F (..\STM32F10x_StdPeriph_Driver\src\stm32f10x_fsmc.c)(0x4D783BB4)(-c --cpu Cortex-M3 -g -O0 --apcs=interwork -I..\CMSIS\CoreSupport -I..\CMSIS\DeviceSupport\STM32F10x -I..\STM32F10x_StdPeriph_Driver\inc -I..\User 

-I "F:\keil  MDK\ARM\RV31\INC" 

-I "F:\keil  MDK\ARM\CMSIS\Include" 

-I "F:\keil  MDK\ARM\Inc\ST\STM32F10x" 

-DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD -o ".\Obj\stm32f10x_fsmc.o" --omf_browse ".\Obj\stm32f10x_fsmc.crf" --depend ".\Obj\stm32f10x_fsmc.d")
F (..\STM32F10x_StdPeriph_Driver\src\stm32f10x_gpio.c)(0x4D79EEC6)(-c --cpu Cortex-M3 -g -O0 --apcs=interwork -I..\CMSIS\CoreSupport -I..\CMSIS\DeviceSupport\STM32F10x -I..\STM32F10x_StdPeriph_Driver\inc -I..\User 

-I "F:\keil  MDK\ARM\RV31\INC" 

-I "F:\keil  MDK\ARM\CMSIS\Include" 

-I "F:\keil  MDK\ARM\Inc\ST\STM32F10x" 

-DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD -o ".\Obj\stm32f10x_gpio.o" --omf_browse ".\Obj\stm32f10x_gpio.crf" --depend ".\Obj\stm32f10x_gpio.d")
F (..\STM32F10x_StdPeriph_Driver\src\stm32f10x_i2c.c)(0x4D783BB4)(-c --cpu Cortex-M3 -g -O0 --apcs=interwork -I..\CMSIS\CoreSupport -I..\CMSIS\DeviceSupport\STM32F10x -I..\STM32F10x_StdPeriph_Driver\inc -I..\User 

-I "F:\keil  MDK\ARM\RV31\INC" 

-I "F:\keil  MDK\ARM\CMSIS\Include" 

-I "F:\keil  MDK\ARM\Inc\ST\STM32F10x" 

-DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD -o ".\Obj\stm32f10x_i2c.o" --omf_browse ".\Obj\stm32f10x_i2c.crf" --depend ".\Obj\stm32f10x_i2c.d")
F (..\STM32F10x_StdPeriph_Driver\src\stm32f10x_iwdg.c)(0x4D783BB4)(-c --cpu Cortex-M3 -g -O0 --apcs=interwork -I..\CMSIS\CoreSupport -I..\CMSIS\DeviceSupport\STM32F10x -I..\STM32F10x_StdPeriph_Driver\inc -I..\User 

-I "F:\keil  MDK\ARM\RV31\INC" 

-I "F:\keil  MDK\ARM\CMSIS\Include" 

-I "F:\keil  MDK\ARM\Inc\ST\STM32F10x" 

-DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD -o ".\Obj\stm32f10x_iwdg.o" --omf_browse ".\Obj\stm32f10x_iwdg.crf" --depend ".\Obj\stm32f10x_iwdg.d")
F (..\STM32F10x_StdPeriph_Driver\src\stm32f10x_pwr.c)(0x4D783BB4)(-c --cpu Cortex-M3 -g -O0 --apcs=interwork -I..\CMSIS\CoreSupport -I..\CMSIS\DeviceSupport\STM32F10x -I..\STM32F10x_StdPeriph_Driver\inc -I..\User 

-I "F:\keil  MDK\ARM\RV31\INC" 

-I "F:\keil  MDK\ARM\CMSIS\Include" 

-I "F:\keil  MDK\ARM\Inc\ST\STM32F10x" 

-DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD -o ".\Obj\stm32f10x_pwr.o" --omf_browse ".\Obj\stm32f10x_pwr.crf" --depend ".\Obj\stm32f10x_pwr.d")
F (..\STM32F10x_StdPeriph_Driver\src\stm32f10x_rcc.c)(0x4D783BB4)(-c --cpu Cortex-M3 -g -O0 --apcs=interwork -I..\CMSIS\CoreSupport -I..\CMSIS\DeviceSupport\STM32F10x -I..\STM32F10x_StdPeriph_Driver\inc -I..\User 

-I "F:\keil  MDK\ARM\RV31\INC" 

-I "F:\keil  MDK\ARM\CMSIS\Include" 

-I "F:\keil  MDK\ARM\Inc\ST\STM32F10x" 

-DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD -o ".\Obj\stm32f10x_rcc.o" --omf_browse ".\Obj\stm32f10x_rcc.crf" --depend ".\Obj\stm32f10x_rcc.d")
F (..\STM32F10x_StdPeriph_Driver\src\stm32f10x_rtc.c)(0x4D783BB4)(-c --cpu Cortex-M3 -g -O0 --apcs=interwork -I..\CMSIS\CoreSupport -I..\CMSIS\DeviceSupport\STM32F10x -I..\STM32F10x_StdPeriph_Driver\inc -I..\User 

-I "F:\keil  MDK\ARM\RV31\INC" 

-I "F:\keil  MDK\ARM\CMSIS\Include" 

-I "F:\keil  MDK\ARM\Inc\ST\STM32F10x" 

-DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD -o ".\Obj\stm32f10x_rtc.o" --omf_browse ".\Obj\stm32f10x_rtc.crf" --depend ".\Obj\stm32f10x_rtc.d")
F (..\STM32F10x_StdPeriph_Driver\src\stm32f10x_sdio.c)(0x4D783BB4)(-c --cpu Cortex-M3 -g -O0 --apcs=interwork -I..\CMSIS\CoreSupport -I..\CMSIS\DeviceSupport\STM32F10x -I..\STM32F10x_StdPeriph_Driver\inc -I..\User 

-I "F:\keil  MDK\ARM\RV31\INC" 

-I "F:\keil  MDK\ARM\CMSIS\Include" 

-I "F:\keil  MDK\ARM\Inc\ST\STM32F10x" 

-DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD -o ".\Obj\stm32f10x_sdio.o" --omf_browse ".\Obj\stm32f10x_sdio.crf" --depend ".\Obj\stm32f10x_sdio.d")
F (..\STM32F10x_StdPeriph_Driver\src\stm32f10x_spi.c)(0x4D783BB4)(-c --cpu Cortex-M3 -g -O0 --apcs=interwork -I..\CMSIS\CoreSupport -I..\CMSIS\DeviceSupport\STM32F10x -I..\STM32F10x_StdPeriph_Driver\inc -I..\User 

-I "F:\keil  MDK\ARM\RV31\INC" 

-I "F:\keil  MDK\ARM\CMSIS\Include" 

-I "F:\keil  MDK\ARM\Inc\ST\STM32F10x" 

-DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD -o ".\Obj\stm32f10x_spi.o" --omf_browse ".\Obj\stm32f10x_spi.crf" --depend ".\Obj\stm32f10x_spi.d")
F (..\STM32F10x_StdPeriph_Driver\src\stm32f10x_tim.c)(0x4D783BB4)(-c --cpu Cortex-M3 -g -O0 --apcs=interwork -I..\CMSIS\CoreSupport -I..\CMSIS\DeviceSupport\STM32F10x -I..\STM32F10x_StdPeriph_Driver\inc -I..\User 

-I "F:\keil  MDK\ARM\RV31\INC" 

-I "F:\keil  MDK\ARM\CMSIS\Include" 

-I "F:\keil  MDK\ARM\Inc\ST\STM32F10x" 

-DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD -o ".\Obj\stm32f10x_tim.o" --omf_browse ".\Obj\stm32f10x_tim.crf" --depend ".\Obj\stm32f10x_tim.d")
F (..\STM32F10x_StdPeriph_Driver\src\stm32f10x_usart.c)(0x4D783BB4)(-c --cpu Cortex-M3 -g -O0 --apcs=interwork -I..\CMSIS\CoreSupport -I..\CMSIS\DeviceSupport\STM32F10x -I..\STM32F10x_StdPeriph_Driver\inc -I..\User 

-I "F:\keil  MDK\ARM\RV31\INC" 

-I "F:\keil  MDK\ARM\CMSIS\Include" 

-I "F:\keil  MDK\ARM\Inc\ST\STM32F10x" 

-DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD -o ".\Obj\stm32f10x_usart.o" --omf_browse ".\Obj\stm32f10x_usart.crf" --depend ".\Obj\stm32f10x_usart.d")
F (..\STM32F10x_StdPeriph_Driver\src\stm32f10x_wwdg.c)(0x4D783BB4)(-c --cpu Cortex-M3 -g -O0 --apcs=interwork -I..\CMSIS\CoreSupport -I..\CMSIS\DeviceSupport\STM32F10x -I..\STM32F10x_StdPeriph_Driver\inc -I..\User 

-I "F:\keil  MDK\ARM\RV31\INC" 

-I "F:\keil  MDK\ARM\CMSIS\Include" 

-I "F:\keil  MDK\ARM\Inc\ST\STM32F10x" 

-DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD -o ".\Obj\stm32f10x_wwdg.o" --omf_browse ".\Obj\stm32f10x_wwdg.crf" --depend ".\Obj\stm32f10x_wwdg.d")
F (..\CMSIS\CoreSupport\core_cm3.c)(0x4C0C587E)(-c --cpu Cortex-M3 -g -O0 --apcs=interwork -I..\CMSIS\CoreSupport -I..\CMSIS\DeviceSupport\STM32F10x -I..\STM32F10x_StdPeriph_Driver\inc -I..\User 

-I "F:\keil  MDK\ARM\RV31\INC" 

-I "F:\keil  MDK\ARM\CMSIS\Include" 

-I "F:\keil  MDK\ARM\Inc\ST\STM32F10x" 

-DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD -o ".\Obj\core_cm3.o" --omf_browse ".\Obj\core_cm3.crf" --depend ".\Obj\core_cm3.d")
F (..\CMSIS\DeviceSupport\STM32F10x\system_stm32f10x.c)(0x4D783CB0)(-c --cpu Cortex-M3 -g -O0 --apcs=interwork -I..\CMSIS\CoreSupport -I..\CMSIS\DeviceSupport\STM32F10x -I..\STM32F10x_StdPeriph_Driver\inc -I..\User 

-I "F:\keil  MDK\ARM\RV31\INC" 

-I "F:\keil  MDK\ARM\CMSIS\Include" 

-I "F:\keil  MDK\ARM\Inc\ST\STM32F10x" 

-DUSE_STDPERIPH_DRIVER -DSTM32F10X_HD -o ".\Obj\system_stm32f10x.o" --omf_browse ".\Obj\system_stm32f10x.crf" --depend ".\Obj\system_stm32f10x.d")
F (..\CMSIS\DeviceSupport\STM32F10x\startup_stm32f10x_hd.s)(0x4D783CDE)(--cpu Cortex-M3 -g --apcs=interwork 

-I "F:\keil  MDK\ARM\RV31\INC" 

-I "F:\keil  MDK\ARM\CMSIS\Include" 

-I "F:\keil  MDK\ARM\Inc\ST\STM32F10x" 

--list ".\List\startup_stm32f10x_hd.lst" --xref -o ".\Obj\startup_stm32f10x_hd.o" --depend ".\Obj\startup_stm32f10x_hd.d")
